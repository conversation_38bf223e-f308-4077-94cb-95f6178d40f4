const express = require('express');
const creatorController = require('../../controllers/admin/creator.controller');

const router = express.Router();

// Creator routes
router.get('/', creatorController.getAllCreators);
router.get('/verification-stats', creatorController.getVerificationStats);
router.get('/onboarding-stats', creatorController.getOnboardingStats);
router.get('/:id', creatorController.getCreator);
router.patch('/:id/verification', creatorController.updateVerificationStatus);
router.delete('/:id', creatorController.deleteCreator);

// Creator routes (only the ones related to updating details and resetting password)
router.patch('/:id/details', creatorController.updateCreatorDetails);
router.patch('/:id/metrics', creatorController.updateCreatorMetrics);
router.post('/:id/reset-password', creatorController.resetCreatorPassword);

// Metrics recalculation routes
router.post('/recalculate-all-metrics', creatorController.recalculateAllCreatorMetrics);
router.post('/:id/recalculate-metrics', creatorController.recalculateCreatorMetrics);

// Creator's products, bales, orders, reviews, and earnings
router.get('/:id/products', creatorController.getCreatorProducts);
router.get('/:id/bales', creatorController.getCreatorBales);
router.get('/:id/orders', creatorController.getCreatorOrders);
router.get('/:id/reviews', creatorController.getCreatorReviews);
router.get('/:id/earnings', creatorController.getCreatorEarnings);

// Business verification
router.patch('/:id/business-verification', creatorController.updateBusinessVerification);

module.exports = router;
