const Product = require('../models/product.model');
const { Creator } = require('../models/user.model');
const Category = require('../models/category.model');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

/**
 * Helper function to parse comma-separated values
 */
const parseCommaSeparated = (value) => {
  if (!value) return [];
  if (Array.isArray(value)) return value;
  return value.split(',').map(item => item.trim()).filter(item => item);
};

/**
 * Search products, bales, and shops
 * @route GET /api/v1/search
 * @access Public
 */
exports.search = catchAsync(async (req, res, next) => {
  // Get search query
  const query = req.query.q;
  if (!query) {
    return next(new AppError('Please provide a search query', 400));
  }

  // Get search type (default to 'all')
  const type = req.query.type || 'all';

  // Build base filters for products/bales
  const baseFilters = { status: 'active' };

  // Add category filter if provided
  if (req.query.category) {
    const categories = parseCommaSeparated(req.query.category);
    if (categories.length > 0) {
      baseFilters.$or = [
        { category: { $in: categories } },
        { relatedCategories: { $in: categories } }
      ];
    }
  }

  // Add price range filter if provided
  if (req.query.minPrice || req.query.maxPrice) {
    baseFilters.basePrice = {};
    if (req.query.minPrice) {
      baseFilters.basePrice.$gte = parseFloat(req.query.minPrice);
    }
    if (req.query.maxPrice) {
      baseFilters.basePrice.$lte = parseFloat(req.query.maxPrice);
    }
  }

  // Add creator filter if provided
  if (req.query.creator) {
    baseFilters.creator = req.query.creator;
  }

  // Add gender filter if provided (for products only)
  if (req.query.gender) {
    baseFilters.gender = req.query.gender;
  }

  // Add brands filter if provided (for products only)
  if (req.query.brands) {
    const brands = parseCommaSeparated(req.query.brands);
    if (brands.length > 0) {
      baseFilters.brand = { $in: brands };
    }
  }

  // Add sizes filter if provided
  if (req.query.sizes) {
    const sizes = parseCommaSeparated(req.query.sizes);
    if (sizes.length > 0) {
      baseFilters['variations.size'] = { $in: sizes };
    }
  }

  // Add countries filter if provided (for bales only)
  if (req.query.countries) {
    const countries = parseCommaSeparated(req.query.countries);
    if (countries.length > 0) {
      baseFilters.country = { $in: countries };
    }
  }

  // Add conditions filter if provided (for bales only)
  if (req.query.conditions) {
    const conditions = parseCommaSeparated(req.query.conditions);
    if (conditions.length > 0) {
      baseFilters.condition = { $in: conditions };
    }
  }

  // Create search regex
  const searchRegex = new RegExp(query, 'i');

  // Set pagination parameters
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Set sort parameter (default to newest)
  let sortBy = '-createdAt';
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      case 'discount-desc':
        // Will handle discount sorting after fetching items
        sortBy = '-createdAt'; // Default sort for now
        break;
      case 'ending-soon':
        // Will handle ending soon sorting after fetching items
        sortBy = '-createdAt'; // Default sort for now
        break;
      default:
        sortBy = '-createdAt';
    }
  }

  // Initialize results
  let products = [];
  let bales = [];
  let shops = [];
  let total = 0;

  // Search products
  if (type === 'all' || type === 'products') {
    const productFilters = {
      ...baseFilters,
      type: 'product',
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ]
    };

    // Remove bale-specific filters for products
    delete productFilters.country;
    delete productFilters.condition;

    products = await Product.find(productFilters)
      .populate('creator', 'shopInfo.name shopInfo.logo')
      .sort(sortBy)
      .skip(type === 'all' ? 0 : skip)
      .limit(type === 'all' ? Math.floor(limit / 3) : limit)
      .select('name brand basePrice images ratingsAverage ratingsQuantity gender variations type creator status');

    if (type === 'products') {
      total = await Product.countDocuments(productFilters);
    }
  }

  // Search bales
  if (type === 'all' || type === 'bales') {
    const baleFilters = {
      ...baseFilters,
      type: 'bale',
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ]
    };

    // Remove product-specific filters for bales
    delete baleFilters.brand;
    delete baleFilters.gender;

    bales = await Product.find(baleFilters)
      .populate('creator', 'shopInfo.name shopInfo.logo')
      .sort(sortBy)
      .skip(type === 'all' ? 0 : skip)
      .limit(type === 'all' ? Math.floor(limit / 3) : limit)
      .select('name basePrice images ratingsAverage ratingsQuantity country condition variations type creator totalItems weight status');

    if (type === 'bales') {
      total = await Product.countDocuments(baleFilters);
    }
  }

  // Search shops/creators
  if (type === 'all' || type === 'shops') {
    const shopQuery = {
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'shopInfo.description': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    };

    shops = await Creator.find(shopQuery)
      .sort('-metrics.followers')
      .skip(type === 'all' ? 0 : skip)
      .limit(type === 'all' ? Math.floor(limit / 3) : limit)
      .select('name photo shopInfo metrics businessInfo');

    if (type === 'shops') {
      total = await Creator.countDocuments(shopQuery);
    }
  }

  // Calculate total for 'all' type
  if (type === 'all') {
    // Build product count query
    const productCountFilters = {
      ...baseFilters,
      type: 'product',
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { 'specifications.mainMaterial': searchRegex },
        { brand: searchRegex },
        { tags: searchRegex }
      ]
    };
    delete productCountFilters.country;
    delete productCountFilters.condition;

    // Build bale count query
    const baleCountFilters = {
      ...baseFilters,
      type: 'bale',
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex },
        { tags: searchRegex }
      ]
    };
    delete baleCountFilters.brand;
    delete baleCountFilters.gender;

    const shopCountQuery = {
      $or: [
        { name: searchRegex },
        { 'shopInfo.name': searchRegex },
        { 'shopInfo.description': searchRegex },
        { 'businessInfo.businessName': searchRegex }
      ],
      verificationStatus: 'verified',
      onboardingStatus: 'completed'
    };

    const [productCount, baleCount, shopCount] = await Promise.all([
      Product.countDocuments(productCountFilters),
      Product.countDocuments(baleCountFilters),
      Creator.countDocuments(shopCountQuery)
    ]);

    total = productCount + baleCount + shopCount;
  }

  // Process products to return enhanced data structure
  const processedProducts = products.map(product => {
    const productObj = product.toObject();

    // Return enhanced product object with all virtuals
    return {
      _id: productObj._id,
      type: productObj.type,
      name: productObj.name,
      images: productObj.images || [],
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage || 0,
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0,
      formattedPriceRange: product.formattedPriceRange,
      hasAnyDiscount: product.hasAnyDiscount || false,
      availableColors: product.availableColors || [],
      availableSizes: product.availableSizes || [],
      totalItemsLeft: product.totalStock || 0,
      brand: productObj.brand,
      gender: productObj.gender,
      shop: productObj.creator ? {
        name: productObj.creator.shopInfo?.name,
        logo: productObj.creator.shopInfo?.logo
      } : null
    };
  });

  // Process bales to return enhanced data structure
  const processedBales = bales.map(bale => {
    const baleObj = bale.toObject();

    // Return enhanced bale object with all virtuals
    return {
      _id: baleObj._id,
      type: baleObj.type,
      name: baleObj.name,
      images: baleObj.images || [],
      basePrice: baleObj.basePrice,
      status: baleObj.status,
      maxDiscountPercentage: bale.maxDiscountPercentage || 0,
      ratingsAverage: baleObj.ratingsAverage || 0,
      ratingsQuantity: baleObj.ratingsQuantity || 0,
      formattedPriceRange: bale.formattedPriceRange,
      hasAnyDiscount: bale.hasAnyDiscount || false,
      availableIdentifiers: bale.availableIdentifiers || [], // For bales, use identifiers instead of colors/sizes
      totalItemsLeft: bale.totalStock || 0,
      country: baleObj.country,
      condition: baleObj.condition,
      totalItems: baleObj.totalItems,
      weight: baleObj.weight,
      shop: baleObj.creator ? {
        name: baleObj.creator.shopInfo?.name,
        logo: baleObj.creator.shopInfo?.logo
      } : null
    };
  });

  // Process shops to add type and format data
  const processedShops = shops.map(shop => {
    const shopObj = shop.toObject();

    return {
      _id: shopObj._id,
      type: 'shop',
      name: shopObj.shopInfo?.name || shopObj.businessInfo?.businessName || shopObj.name,
      logo: shopObj.shopInfo?.logo || shopObj.photo,
      description: shopObj.shopInfo?.description || '',
      followers: shopObj.metrics?.followers || 0,
      metrics: {
        totalProducts: shopObj.metrics?.totalProducts || 0,
        totalBales: shopObj.metrics?.totalBales || 0,
        averageRating: shopObj.metrics?.averageRating || 0,
        totalSales: shopObj.metrics?.totalSales || 0,
        qualityScore: shopObj.metrics?.qualityScore || 0
      }
    };
  });

  // Apply special sorting if needed
  let allItems = [...processedProducts, ...processedBales];

  if (req.query.sort === 'discount-desc') {
    allItems.sort((a, b) => {
      const discountA = a.maxDiscountPercentage || 0;
      const discountB = b.maxDiscountPercentage || 0;
      return discountB - discountA;
    });
  } else if (req.query.sort === 'ending-soon') {
    // First filter items on sale
    const saleItems = allItems.filter(item => item.hasAnyDiscount);
    const nonSaleItems = allItems.filter(item => !item.hasAnyDiscount);

    // Sort sale items by discount percentage (highest first)
    saleItems.sort((a, b) => (b.maxDiscountPercentage || 0) - (a.maxDiscountPercentage || 0));

    // Combine sorted sale items with non-sale items
    allItems = [...saleItems, ...nonSaleItems];
  }

  // Apply pagination if special sorting was used and not searching all types
  if (['discount-desc', 'ending-soon'].includes(req.query.sort) && type !== 'all') {
    const startIndex = skip;
    const endIndex = skip + limit;

    if (type === 'products') {
      const productItems = allItems.filter(item => item.type === 'product');
      processedProducts.splice(0, processedProducts.length, ...productItems.slice(startIndex, endIndex));
    } else if (type === 'bales') {
      const baleItems = allItems.filter(item => item.type === 'bale');
      processedBales.splice(0, processedBales.length, ...baleItems.slice(startIndex, endIndex));
    }
  }

  // Apply pagination for 'all' type or when no special sorting
  if (type === 'all') {
    // Limit results for 'all' type to show balanced results
    const maxPerType = Math.floor(limit / 3);
    processedProducts.splice(maxPerType);
    processedBales.splice(maxPerType);
    processedShops.splice(maxPerType);
  } else if (!['discount-desc', 'ending-soon'].includes(req.query.sort)) {
    // Apply normal pagination for single type searches without special sorting
    if (type === 'products') {
      const startIndex = skip;
      const endIndex = skip + limit;
      processedProducts.splice(0, processedProducts.length, ...processedProducts.slice(startIndex, endIndex));
    } else if (type === 'bales') {
      const startIndex = skip;
      const endIndex = skip + limit;
      processedBales.splice(0, processedBales.length, ...processedBales.slice(startIndex, endIndex));
    } else if (type === 'shops') {
      const startIndex = skip;
      const endIndex = skip + limit;
      processedShops.splice(0, processedShops.length, ...processedShops.slice(startIndex, endIndex));
    }
  }

  res.status(200).json({
    status: 'success',
    results: processedProducts.length + processedBales.length + processedShops.length,
    total,
    page,
    limit,
    data: {
      products: processedProducts,
      bales: processedBales,
      shops: processedShops
    }
  });
});

/**
 * Get search filters (categories, price ranges, brands, etc.)
 * @route GET /api/v1/search/filters
 * @access Public
 */
exports.getSearchFilters = catchAsync(async (req, res, next) => {
  // Get categories with hierarchy (levels 0 and 1 only)
  const categories = await Category.find()
    .select('name description parent')
    .sort('-name'); // Reverse alphabetical as per user preference

  // Get aggregated filter data for products and bales
  const filterData = await Product.aggregate([
    { $match: { status: 'active' } },
    {
      $facet: {
        // Get price ranges for products
        productPriceStats: [
          { $match: { type: 'product' } },
          {
            $group: {
              _id: null,
              minPrice: { $min: '$basePrice' },
              maxPrice: { $max: '$basePrice' },
              avgPrice: { $avg: '$basePrice' }
            }
          }
        ],
        // Get price ranges for bales
        balePriceStats: [
          { $match: { type: 'bale' } },
          {
            $group: {
              _id: null,
              minPrice: { $min: '$basePrice' },
              maxPrice: { $max: '$basePrice' },
              avgPrice: { $avg: '$basePrice' }
            }
          }
        ],
        // Get brands from products
        brands: [
          { $match: { type: 'product', brand: { $ne: null, $ne: '' } } },
          { $group: { _id: '$brand' } },
          { $sort: { _id: 1 } }
        ],
        // Get sizes from variations
        sizes: [
          { $unwind: '$variations' },
          { $match: { 'variations.size': { $ne: null, $ne: '' } } },
          { $group: { _id: '$variations.size' } },
          { $sort: { _id: 1 } }
        ],
        // Get countries from bales
        countries: [
          { $match: { type: 'bale', country: { $ne: null, $ne: '' } } },
          { $group: { _id: '$country' } },
          { $sort: { _id: 1 } }
        ],
        // Get conditions
        conditions: [
          { $match: { condition: { $ne: null, $ne: '' } } },
          { $group: { _id: '$condition' } },
          { $sort: { _id: 1 } }
        ]
      }
    }
  ]);

  // Get top shops
  const topShops = await Creator.find({
    verificationStatus: 'verified',
    onboardingStatus: 'completed'
  })
    .sort('-metrics.followers')
    .limit(20)
    .select('name photo shopInfo businessInfo metrics');

  const data = filterData[0];

  res.status(200).json({
    status: 'success',
    data: {
      categories,
      priceStats: {
        products: data.productPriceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 },
        bales: data.balePriceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 }
      },
      brands: data.brands.map(brand => brand._id),
      sizes: data.sizes.map(size => size._id),
      countries: data.countries.map(country => country._id),
      conditions: data.conditions.map(condition => condition._id),
      topShops: topShops.map(shop => ({
        _id: shop._id,
        name: shop.shopInfo?.name || shop.businessInfo?.businessName || shop.name,
        logo: shop.shopInfo?.logo || shop.photo,
        followers: shop.metrics?.followers || 0,
        totalProducts: shop.metrics?.totalProducts || 0,
        totalBales: shop.metrics?.totalBales || 0,
        averageRating: shop.metrics?.averageRating || 0
      }))
    }
  });
});
