const { Creator } = require('../models/user.model');
const Product = require('../models/product.model');
const Order = require('../models/order.model');
const Review = require('../models/review.model');
const Buyer = require('../models/buyer.model');

/**
 * Recalculate and update metrics for a specific creator
 * @param {String} creatorId - The creator's ID
 * @returns {Object} Updated metrics
 */
const recalculateCreatorMetrics = async (creatorId) => {
  try {
    console.log(`Recalculating metrics for creator: ${creatorId}`);

    // Get product and bale counts by status
    const [
      totalProducts,
      totalBales,
      activeProducts,
      activeBales,
      pendingProducts,
      pendingBales,
      inactiveProducts,
      inactiveBales
    ] = await Promise.all([
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'active' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'pending' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'pending' }),
      Product.countDocuments({ creator: creatorId, type: 'product', status: 'inactive' }),
      Product.countDocuments({ creator: creatorId, type: 'bale', status: 'inactive' })
    ]);

    // Get order statistics (last 30 days)
    const orderStats = await Order.aggregate([
      {
        $match: {
          'items.creator': creatorId,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $unwind: '$items'
      },
      {
        $match: {
          'items.creator': creatorId
        }
      },
      {
        $group: {
          _id: null,
          totalSales: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
        }
      }
    ]);

    // Get shipping statistics
    const shippingStats = await Order.aggregate([
      {
        $match: {
          'items.creator': creatorId,
          status: 'delivered',
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $addFields: {
          shippingDays: {
            $divide: [
              { $subtract: ['$deliveredAt', '$shippedAt'] },
              1000 * 60 * 60 * 24
            ]
          },
          isOnTime: {
            $lte: [
              { $divide: [{ $subtract: ['$deliveredAt', '$shippedAt'] }, 1000 * 60 * 60 * 24] },
              7
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgShippingDays: { $avg: '$shippingDays' },
          totalDelivered: { $sum: 1 },
          totalOnTime: { $sum: { $cond: ['$isOnTime', 1, 0] } }
        }
      },
      {
        $project: {
          avgShippingDays: 1,
          onTimeRate: {
            $cond: [
              { $eq: ['$totalDelivered', 0] },
              0,
              { $multiply: [{ $divide: ['$totalOnTime', '$totalDelivered'] }, 100] }
            ]
          }
        }
      }
    ]);

    // Get review statistics
    const reviewStats = await Review.aggregate([
      {
        $match: {
          creator: creatorId,
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: 1 }
        }
      }
    ]);

    // Get follower count
    const followerCount = await Buyer.countDocuments({ followedCreators: creatorId });

    // Calculate quality score
    const avgRating = reviewStats[0]?.averageRating || 0;
    const onTimeRate = shippingStats[0]?.onTimeRate || 0;
    const qualityScore = Math.round((avgRating * 20 * 0.5) + (onTimeRate * 0.5));

    // Prepare metrics update
    const metricsUpdate = {
      'metrics.totalProducts': totalProducts,
      'metrics.totalBales': totalBales,
      'metrics.activeProducts': activeProducts,
      'metrics.activeBales': activeBales,
      'metrics.pendingProducts': pendingProducts,
      'metrics.pendingBales': pendingBales,
      'metrics.inactiveProducts': inactiveProducts,
      'metrics.inactiveBales': inactiveBales,
      'metrics.totalSales': orderStats[0]?.totalSales || 0,
      'metrics.totalRevenue': orderStats[0]?.totalRevenue || 0,
      'metrics.averageRating': avgRating,
      'metrics.qualityScore': qualityScore,
      'metrics.shippingSpeed': shippingStats[0]?.avgShippingDays || 0,
      'metrics.followers': followerCount
    };

    // Update creator metrics
    const updatedCreator = await Creator.findByIdAndUpdate(
      creatorId,
      { $set: metricsUpdate },
      { new: true, runValidators: true }
    );

    console.log(`✅ Updated metrics for creator ${creatorId}:`, {
      totalProducts,
      totalBales,
      activeProducts,
      activeBales,
      totalSales: orderStats[0]?.totalSales || 0,
      followers: followerCount
    });

    return updatedCreator?.metrics;
  } catch (error) {
    console.error(`❌ Error recalculating metrics for creator ${creatorId}:`, error);
    throw error;
  }
};

/**
 * Recalculate metrics for all creators
 * @param {Object} options - Options for the recalculation
 * @param {Number} options.batchSize - Number of creators to process at once
 * @param {Boolean} options.onlyZeroMetrics - Only update creators with zero total products/bales
 * @returns {Object} Summary of the operation
 */
const recalculateAllCreatorMetrics = async (options = {}) => {
  const { batchSize = 10, onlyZeroMetrics = false } = options;
  
  try {
    console.log('🔄 Starting creator metrics recalculation...');

    // Build query filter
    const filter = {};
    if (onlyZeroMetrics) {
      filter.$or = [
        { 'metrics.totalProducts': 0 },
        { 'metrics.totalBales': 0 },
        { 'metrics.totalProducts': { $exists: false } },
        { 'metrics.totalBales': { $exists: false } }
      ];
    }

    // Get all creators that need metrics update
    const creators = await Creator.find(filter).select('_id').lean();
    
    console.log(`📊 Found ${creators.length} creators to update`);

    let processed = 0;
    let errors = 0;
    const results = [];

    // Process creators in batches
    for (let i = 0; i < creators.length; i += batchSize) {
      const batch = creators.slice(i, i + batchSize);
      
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(creators.length / batchSize)}`);

      const batchPromises = batch.map(async (creator) => {
        try {
          const metrics = await recalculateCreatorMetrics(creator._id);
          processed++;
          return { creatorId: creator._id, success: true, metrics };
        } catch (error) {
          errors++;
          console.error(`Failed to update creator ${creator._id}:`, error.message);
          return { creatorId: creator._id, success: false, error: error.message };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map(result => result.value || result.reason));

      // Add a small delay between batches to avoid overwhelming the database
      if (i + batchSize < creators.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`✅ Metrics recalculation completed!`);
    console.log(`📈 Processed: ${processed}, Errors: ${errors}, Total: ${creators.length}`);

    return {
      total: creators.length,
      processed,
      errors,
      results
    };
  } catch (error) {
    console.error('❌ Error in bulk metrics recalculation:', error);
    throw error;
  }
};

module.exports = {
  recalculateCreatorMetrics,
  recalculateAllCreatorMetrics
};
