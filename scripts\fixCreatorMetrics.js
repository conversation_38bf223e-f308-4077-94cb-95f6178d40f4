#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix creator metrics
 * This script recalculates and updates the metrics for creators who have zero counts
 * but actually have active products/bales
 * 
 * Usage:
 * node scripts/fixCreatorMetrics.js [options]
 * 
 * Options:
 * --all              Recalculate metrics for all creators
 * --creator-id=ID    Recalculate metrics for specific creator
 * --batch-size=N     Process N creators at a time (default: 10)
 * --help             Show this help message
 */

const mongoose = require('mongoose');
const { recalculateCreatorMetrics, recalculateAllCreatorMetrics } = require('../utils/recalculateCreatorMetrics');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  all: false,
  creatorId: null,
  batchSize: 10,
  help: false
};

args.forEach(arg => {
  if (arg === '--all') {
    options.all = true;
  } else if (arg === '--help') {
    options.help = true;
  } else if (arg.startsWith('--creator-id=')) {
    options.creatorId = arg.split('=')[1];
  } else if (arg.startsWith('--batch-size=')) {
    options.batchSize = parseInt(arg.split('=')[1]) || 10;
  }
});

// Show help
if (options.help) {
  console.log(`
Creator Metrics Fix Script

This script recalculates and updates the metrics for creators.

Usage:
  node scripts/fixCreatorMetrics.js [options]

Options:
  --all              Recalculate metrics for all creators
  --creator-id=ID    Recalculate metrics for specific creator
  --batch-size=N     Process N creators at a time (default: 10)
  --help             Show this help message

Examples:
  # Fix metrics for all creators with zero counts
  node scripts/fixCreatorMetrics.js

  # Fix metrics for all creators
  node scripts/fixCreatorMetrics.js --all

  # Fix metrics for specific creator
  node scripts/fixCreatorMetrics.js --creator-id=60f7b3b3b3b3b3b3b3b3b3b3

  # Process 20 creators at a time
  node scripts/fixCreatorMetrics.js --all --batch-size=20
`);
  process.exit(0);
}

async function main() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.DATABASE_URI || process.env.DATABASE_LOCAL);
    console.log('✅ Connected to MongoDB');

    if (options.creatorId) {
      // Recalculate metrics for specific creator
      console.log(`🎯 Recalculating metrics for creator: ${options.creatorId}`);
      const metrics = await recalculateCreatorMetrics(options.creatorId);
      console.log('✅ Metrics updated:', metrics);
    } else {
      // Recalculate metrics for all creators (or only those with zero metrics)
      const onlyZeroMetrics = !options.all;
      
      if (onlyZeroMetrics) {
        console.log('🔍 Recalculating metrics for creators with zero counts only...');
      } else {
        console.log('🔄 Recalculating metrics for ALL creators...');
      }

      const result = await recalculateAllCreatorMetrics({
        batchSize: options.batchSize,
        onlyZeroMetrics
      });

      console.log('\n📊 Summary:');
      console.log(`Total creators: ${result.total}`);
      console.log(`Successfully processed: ${result.processed}`);
      console.log(`Errors: ${result.errors}`);

      if (result.errors > 0) {
        console.log('\n❌ Creators with errors:');
        result.results
          .filter(r => !r.success)
          .forEach(r => {
            console.log(`  - ${r.creatorId}: ${r.error}`);
          });
      }

      console.log('\n✅ Metrics recalculation completed!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
    process.exit(0);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err.message);
  process.exit(1);
});

// Run the script
main();
