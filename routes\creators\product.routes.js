const express = require('express');
const productController = require('../../controllers/creators/product.controller');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');


const router = express.Router();


// Product routes
router.get('/', productController.getMyProducts);
router.post('/',
  uploadMiddleware.uploadProductImages,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  productController.createProduct
);
router.get('/counts', productController.getProductCounts);
router.get('/stats', productController.getProductStats);
router.get('/:id', productController.getMyProduct);

router.delete('/:id', productController.deleteMyProduct);

// Specific product update routes
router.patch('/:id/basic-info',
  productController.updateBasicInfo
);
router.patch('/:id/specifications',
  uploadMiddleware.processNestedFormData,
  productController.updateSpecifications
);

router.patch('/:id/images',
  uploadMiddleware.uploadProductImages,
  // uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.processProductImagesUpdate,
  uploadMiddleware.handleUploadError,
  productController.updateImages
);

// Variation routes
router.get('/:id/variations', productController.getVariations);
router.post('/:id/variations',
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  productController.addVariation
);

router.patch('/:id/variations',
  productController.updateVariationsAndRelatedCategories
);

router.patch('/:id/variations/:variationId',
  uploadMiddleware.uploadProductImages,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  productController.updateVariation
);
router.delete('/:id/variations/:variationId', productController.deleteVariation);

// Product analytics routes
router.get('/:id/reviews', productController.getProductReviews);
router.get('/:id/sales', productController.getProductSales);

// Product promotion routes
router.get('/:id/promotions', productController.getProductPromotions);
router.post('/:id/promotions', productController.joinProductPromotion);
router.patch('/:id/promotions/:promotionId', productController.updateProductPromotion);
router.delete('/:id/promotions/:promotionId', productController.leaveProductPromotion);

module.exports = router;
