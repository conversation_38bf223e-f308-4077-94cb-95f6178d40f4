/**
 * Test script to verify the complete category hierarchy endpoint
 * This script demonstrates the expected nested structure
 */

const mongoose = require('mongoose');
const Category = require('./models/category.model');

// Example of the expected nested structure
const expectedHierarchyStructure = {
  "status": "success",
  "results": 2,
  "data": {
    "categories": [
      {
        "_id": "68052589174155b4fb3b4292",
        "name": "Women's Fashion",
        "description": "Women's Fashion",
        "featured": false,
        "order": 0,
        "active": true,
        "slug": "women's-fashion",
        "productCount": 15,
        "baleCount": 3,
        "totalCount": 18,
        "immediateChildren": [
          {
            "_id": "68052590174155b4fb3b42d1",
            "name": "Women's Fashion > Footwear",
            "description": "Footwear",
            "featured": false,
            "order": 0,
            "active": true,
            "slug": "women's-fashion-greater-footwear",
            "productCount": 8,
            "baleCount": 1,
            "totalCount": 9,
            "immediateChildren": [
              {
                "_id": "68052591174155b4fb3b4310",
                "name": "Women's Fashion > Footwear > Heels",
                "description": "High heels and dress shoes",
                "featured": false,
                "order": 0,
                "active": true,
                "slug": "women's-fashion-footwear-heels",
                "productCount": 5,
                "baleCount": 0,
                "totalCount": 5,
                "immediateChildren": [
                  {
                    "_id": "68052592174155b4fb3b434f",
                    "name": "Women's Fashion > Footwear > Heels > Stilettos",
                    "description": "Stiletto heels",
                    "featured": false,
                    "order": 0,
                    "active": true,
                    "slug": "women's-fashion-footwear-heels-stilettos",
                    "productCount": 2,
                    "baleCount": 0,
                    "totalCount": 2,
                    "immediateChildren": [],
                    "childrenCount": 0
                  }
                ],
                "childrenCount": 1
              },
              {
                "_id": "68052593174155b4fb3b438e",
                "name": "Women's Fashion > Footwear > Flats",
                "description": "Flat shoes and sandals",
                "featured": false,
                "order": 1,
                "active": true,
                "slug": "women's-fashion-footwear-flats",
                "productCount": 3,
                "baleCount": 1,
                "totalCount": 4,
                "immediateChildren": [],
                "childrenCount": 0
              }
            ],
            "childrenCount": 2
          },
          {
            "_id": "68052594174155b4fb3b43cd",
            "name": "Women's Fashion > Clothing",
            "description": "Women's clothing items",
            "featured": true,
            "order": 1,
            "active": true,
            "slug": "women's-fashion-clothing",
            "productCount": 7,
            "baleCount": 2,
            "totalCount": 9,
            "immediateChildren": [
              {
                "_id": "68052595174155b4fb3b440c",
                "name": "Women's Fashion > Clothing > Dresses",
                "description": "All types of dresses",
                "featured": false,
                "order": 0,
                "active": true,
                "slug": "women's-fashion-clothing-dresses",
                "productCount": 4,
                "baleCount": 1,
                "totalCount": 5,
                "immediateChildren": [],
                "childrenCount": 0
              }
            ],
            "childrenCount": 1
          }
        ],
        "childrenCount": 2
      },
      {
        "_id": "68052596174155b4fb3b444b",
        "name": "Men's Fashion",
        "description": "Men's Fashion",
        "featured": false,
        "order": 1,
        "active": true,
        "slug": "men's-fashion",
        "productCount": 12,
        "baleCount": 2,
        "totalCount": 14,
        "immediateChildren": [
          {
            "_id": "68052597174155b4fb3b448a",
            "name": "Men's Fashion > Clothing",
            "description": "Men's clothing items",
            "featured": false,
            "order": 0,
            "active": true,
            "slug": "men's-fashion-clothing",
            "productCount": 12,
            "baleCount": 2,
            "totalCount": 14,
            "immediateChildren": [],
            "childrenCount": 0
          }
        ],
        "childrenCount": 1
      }
    ],
    "totalParents": 2,
    "totalChildren": 6
  }
};

/**
 * Function to test the hierarchy endpoint
 */
async function testCategoryHierarchy() {
  try {
    console.log('🧪 Testing Category Hierarchy Endpoint...\n');
    
    // Test basic hierarchy
    console.log('📋 Expected Structure:');
    console.log('- Parent categories (level 0)');
    console.log('  - Immediate children (level 1)');
    console.log('    - Sub-children (level 2)');
    console.log('      - Sub-sub-children (level 3)');
    console.log('        - ... (continues to any depth)\n');
    
    console.log('🔍 Key Features:');
    console.log('✅ Complete nested hierarchy (all levels)');
    console.log('✅ Product and bale counts per category');
    console.log('✅ Recursive immediateChildren structure');
    console.log('✅ childrenCount for each level');
    console.log('✅ Proper sorting (order, then name)');
    console.log('✅ Active/inactive filtering');
    console.log('✅ Featured category filtering\n');
    
    console.log('📊 Response Structure:');
    console.log(JSON.stringify(expectedHierarchyStructure, null, 2));
    
    console.log('\n🚀 Test the endpoint with:');
    console.log('GET /api/v1/categories/hierarchy');
    console.log('GET /api/v1/categories/hierarchy?includeCounts=true');
    console.log('GET /api/v1/categories/hierarchy?featured=true');
    console.log('GET /api/v1/categories/hierarchy?includeInactive=true');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testCategoryHierarchy();
}

module.exports = { testCategoryHierarchy, expectedHierarchyStructure };
