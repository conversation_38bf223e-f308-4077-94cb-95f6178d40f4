const mongoose = require('mongoose');
const Category = require('../../models/category.model');
const Product = require('../../models/product.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all categories with hierarchical structure
 * @route GET /api/v1/categories
 * @access Public
 */
exports.getAllCategories = catchAsync(async (req, res, next) => {
  // Get query parameters
  const { featured, withProducts, withBales, includeInactive } = req.query;

  // Build query
  let query = {};
  
  // Filter by featured if specified
  if (featured === 'true') {
    query.featured = true;
  }

  // Include inactive categories only if explicitly requested
  if (includeInactive !== 'true') {
    query.active = true;
  }

  // Get all categories
  let categories = await Category.find(query)
    .select('name description slug parent image icon featured order active')
    .sort('order name');

  // Filter categories that have products if requested
  if (withProducts === 'true') {
    const categoriesWithProducts = await Product.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $match: { count: { $gt: 0 } } }
    ]);
    
    const categoryIdsWithProducts = categoriesWithProducts.map(c => c._id.toString());
    categories = categories.filter(cat => categoryIdsWithProducts.includes(cat._id.toString()));
  }

  // Filter categories that have bales if requested
  if (withBales === 'true') {
    const categoriesWithBales = await Product.aggregate([
      { $match: { status: 'active', type: 'bale' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $match: { count: { $gt: 0 } } }
    ]);

    const categoryIdsWithBales = categoriesWithBales.map(c => c._id.toString());
    categories = categories.filter(cat => categoryIdsWithBales.includes(cat._id.toString()));
  }

  // Build hierarchical structure
  const categoryMap = {};
  const rootCategories = [];

  // Create category map
  categories.forEach(category => {
    categoryMap[category._id] = {
      ...category.toObject(),
      children: []
    };
  });

  // Build hierarchy
  categories.forEach(category => {
    if (category.parent) {
      // This is a child category
      if (categoryMap[category.parent]) {
        categoryMap[category.parent].children.push(categoryMap[category._id]);
      }
    } else {
      // This is a root category
      rootCategories.push(categoryMap[category._id]);
    }
  });

  res.status(200).json({
    status: 'success',
    results: rootCategories.length,
    data: {
      categories: rootCategories
    }
  });
});

/**
 * Get category by ID or slug
 * @route GET /api/v1/categories/:identifier
 * @access Public
 */
exports.getCategory = catchAsync(async (req, res, next) => {
  const { identifier } = req.params;
  let category;

  // Check if identifier is a valid ObjectId
  if (mongoose.Types.ObjectId.isValid(identifier)) {
    // Try to find by ID first, then by slug if not found
    category = await Category.findById(identifier)
      .populate('subcategories', 'name description slug image icon featured order')
      .populate('parent', 'name description slug');

    if (!category) {
      category = await Category.findOne({ slug: identifier })
        .populate('subcategories', 'name description slug image icon featured order')
        .populate('parent', 'name description slug');
    }
  } else {
    // If not a valid ObjectId, search by slug only
    category = await Category.findOne({ slug: identifier })
      .populate('subcategories', 'name description slug image icon featured order')
      .populate('parent', 'name description slug');
  }

  if (!category) {
    return next(new AppError('No category found with that identifier', 404));
  }

  // Get product and bale counts for this category
  const [productCount, baleCount] = await Promise.all([
    Product.countDocuments({ category: category._id, status: 'active', type: 'product' }),
    Product.countDocuments({ category: category._id, status: 'active', type: 'bale' })
  ]);

  // Get subcategory counts
  const subcategoriesWithCounts = await Promise.all(
    category.subcategories.map(async (subcat) => {
      const [subProductCount, subBaleCount] = await Promise.all([
        Product.countDocuments({ category: subcat._id, status: 'active', type: 'product' }),
        Product.countDocuments({ category: subcat._id, status: 'active', type: 'bale' })
      ]);

      return {
        ...subcat.toObject(),
        productCount: subProductCount,
        baleCount: subBaleCount,
        totalCount: subProductCount + subBaleCount
      };
    })
  );

  res.status(200).json({
    status: 'success',
    data: {
      category: {
        ...category.toObject(),
        productCount,
        baleCount,
        totalCount: productCount + baleCount,
        subcategories: subcategoriesWithCounts
      }
    }
  });
});

/**
 * Get category by slug
 * @route GET /api/v1/categories/slug/:slug
 * @access Public
 */
exports.getCategoryBySlug = catchAsync(async (req, res, next) => {
  const { slug } = req.params;

  const category = await Category.findOne({ slug, active: true })
    .populate('subcategories', 'name description slug image icon featured order')
    .populate('parent', 'name description slug');

  if (!category) {
    return next(new AppError('No category found with that slug', 404));
  }

  // Get product and bale counts for this category
  const [productCount, baleCount] = await Promise.all([
    Product.countDocuments({ category: category._id, status: 'active', type: 'product' }),
    Product.countDocuments({ category: category._id, status: 'active', type: 'bale' })
  ]);

  // Get subcategory counts
  const subcategoriesWithCounts = await Promise.all(
    category.subcategories.map(async (subcat) => {
      const [subProductCount, subBaleCount] = await Promise.all([
        Product.countDocuments({ category: subcat._id, status: 'active', type: 'product' }),
        Product.countDocuments({ category: subcat._id, status: 'active', type: 'bale' })
      ]);

      return {
        ...subcat.toObject(),
        productCount: subProductCount,
        baleCount: subBaleCount,
        totalCount: subProductCount + subBaleCount
      };
    })
  );

  res.status(200).json({
    status: 'success',
    data: {
      category: {
        ...category.toObject(),
        productCount,
        baleCount,
        totalCount: productCount + baleCount,
        subcategories: subcategoriesWithCounts
      }
    }
  });
});

/**
 * Get featured categories
 * @route GET /api/v1/categories/featured
 * @access Public
 */
exports.getFeaturedCategories = catchAsync(async (req, res, next) => {
  const limit = req.query.limit * 1 || 10;

  const categories = await Category.find({ 
    featured: true, 
    active: true 
  })
    .select('name description slug image icon order')
    .sort('order name')
    .limit(limit);

  // Get counts for each featured category
  const categoriesWithCounts = await Promise.all(
    categories.map(async (category) => {
      const [productCount, baleCount] = await Promise.all([
        Product.countDocuments({ category: category._id, status: 'active', type: 'product' }),
        Product.countDocuments({ category: category._id, status: 'active', type: 'bale' })
      ]);

      return {
        ...category.toObject(),
        productCount,
        baleCount,
        totalCount: productCount + baleCount
      };
    })
  );

  res.status(200).json({
    status: 'success',
    results: categoriesWithCounts.length,
    data: {
      categories: categoriesWithCounts
    }
  });
});

/**
 * Get parent category with immediate children only (levels 0 and 1)
 * @route GET /api/v1/categories/:identifier/with-children
 * @access Public
 */
exports.getCategoryWithChildren = catchAsync(async (req, res, next) => {
  const { identifier } = req.params;
  let parentCategory;

  // Find parent category by ID or slug
  if (mongoose.Types.ObjectId.isValid(identifier)) {
    parentCategory = await Category.findById(identifier);
  } else {
    parentCategory = await Category.findOne({ slug: identifier });
  }

  if (!parentCategory) {
    return next(new AppError('No category found with that identifier', 404));
  }

  // Get immediate children only (level 1)
  const immediateChildren = await Category.find({
    parent: parentCategory._id,
    active: true
  })
    .select('name description slug image icon featured order')
    .sort('order name');

  // Get product and bale counts for parent category
  const [parentProductCount, parentBaleCount] = await Promise.all([
    Product.countDocuments({ category: parentCategory._id, status: 'active', type: 'product' }),
    Product.countDocuments({ category: parentCategory._id, status: 'active', type: 'bale' })
  ]);

  // Get product and bale counts for each immediate child
  const childrenWithCounts = await Promise.all(
    immediateChildren.map(async (child) => {
      const [childProductCount, childBaleCount] = await Promise.all([
        Product.countDocuments({ category: child._id, status: 'active', type: 'product' }),
        Product.countDocuments({ category: child._id, status: 'active', type: 'bale' })
      ]);

      return {
        ...child.toObject(),
        productCount: childProductCount,
        baleCount: childBaleCount,
        totalCount: childProductCount + childBaleCount
      };
    })
  );

  res.status(200).json({
    status: 'success',
    data: {
      category: {
        ...parentCategory.toObject(),
        productCount: parentProductCount,
        baleCount: parentBaleCount,
        totalCount: parentProductCount + parentBaleCount,
        immediateChildren: childrenWithCounts,
        childrenCount: childrenWithCounts.length
      }
    }
  });
});

/**
 * Get category tree (flat structure with parent references)
 * @route GET /api/v1/categories/tree
 * @access Public
 */
exports.getCategoryTree = catchAsync(async (req, res, next) => {
  const categories = await Category.find({ active: true })
    .select('name description slug parent image icon featured order')
    .populate('parent', 'name slug')
    .sort('order name');

  // Add breadcrumb path for each category
  const categoriesWithPaths = categories.map(category => {
    const breadcrumb = [];
    let current = category;

    // Build breadcrumb path
    while (current) {
      breadcrumb.unshift({
        _id: current._id,
        name: current.name,
        slug: current.slug
      });
      current = current.parent;
    }

    return {
      ...category.toObject(),
      breadcrumb,
      level: breadcrumb.length - 1
    };
  });

  res.status(200).json({
    status: 'success',
    results: categoriesWithPaths.length,
    data: {
      categories: categoriesWithPaths
    }
  });
});

/**
 * Get all parent categories with complete nested hierarchy (all levels)
 * @route GET /api/v1/categories/hierarchy
 * @access Public
 */
exports.getCategoriesHierarchy = catchAsync(async (req, res, next) => {
  // Get query parameters
  const { featured, includeInactive, includeCounts } = req.query;

  // Build query for parent categories (level 0)
  let parentQuery = { parent: null };

  // Filter by featured if specified
  if (featured === 'true') {
    parentQuery.featured = true;
  }

  // Include inactive categories only if explicitly requested
  if (includeInactive !== 'true') {
    parentQuery.active = true;
  }

  // Helper function to recursively build category hierarchy
  const buildCategoryHierarchy = async (parentId, includeInactive, includeCounts) => {
    const children = await Category.find({
      parent: parentId,
      active: includeInactive === 'true' ? { $exists: true } : true
    })
      .select('name description slug image icon featured order active')
      .sort('order -name');

    const childrenWithHierarchy = await Promise.all(
      children.map(async (child) => {
        let childData = child.toObject();

        // Recursively get children of this child
        const immediateChildren = await buildCategoryHierarchy(child._id, includeInactive, includeCounts);

        // Include counts if requested
        if (includeCounts === 'true') {
          const [productCount, baleCount] = await Promise.all([
            Product.countDocuments({ category: child._id, status: 'active', type: 'product' }),
            Product.countDocuments({ category: child._id, status: 'active', type: 'bale' })
          ]);

          childData.productCount = productCount;
          childData.baleCount = baleCount;
          childData.totalCount = productCount + baleCount;
        }

        return {
          ...childData,
          immediateChildren,
          childrenCount: immediateChildren.length
        };
      })
    );

    return childrenWithHierarchy;
  };

  // Get all parent categories (level 0)
  const parentCategories = await Category.find(parentQuery)
    .select('name description slug image icon featured order active')
    .sort('order -name');

  // Build the complete hierarchy for each parent
  const categoriesWithChildren = await Promise.all(
    parentCategories.map(async (parent) => {
      let parentData = parent.toObject();

      // Get complete hierarchy for this parent
      const immediateChildren = await buildCategoryHierarchy(parent._id, includeInactive, includeCounts);

      // Include counts if requested
      if (includeCounts === 'true') {
        const [parentProductCount, parentBaleCount] = await Promise.all([
          Product.countDocuments({ category: parent._id, status: 'active', type: 'product' }),
          Product.countDocuments({ category: parent._id, status: 'active', type: 'bale' })
        ]);

        parentData.productCount = parentProductCount;
        parentData.baleCount = parentBaleCount;
        parentData.totalCount = parentProductCount + parentBaleCount;
      }

      return {
        ...parentData,
        immediateChildren,
        childrenCount: immediateChildren.length
      };
    })
  );

  // Helper function to count total children recursively
  const countTotalChildren = (categories) => {
    return categories.reduce((total, category) => {
      return total + category.childrenCount + countTotalChildren(category.immediateChildren || []);
    }, 0);
  };

  res.status(200).json({
    status: 'success',
    results: categoriesWithChildren.length,
    data: {
      categories: categoriesWithChildren,
      totalParents: categoriesWithChildren.length,
      totalChildren: countTotalChildren(categoriesWithChildren)
    }
  });
});

/**
 * Search categories
 * @route GET /api/v1/categories/search
 * @access Public
 */
exports.searchCategories = catchAsync(async (req, res, next) => {
  const { q, limit = 10 } = req.query;

  if (!q) {
    return next(new AppError('Please provide a search query', 400));
  }

  const categories = await Category.find({
    active: true,
    $or: [
      { name: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } }
    ]
  })
    .select('name description slug image icon featured')
    .limit(limit * 1)
    .sort('featured -order name');

  res.status(200).json({
    status: 'success',
    results: categories.length,
    data: {
      categories,
      query: q
    }
  });
});
